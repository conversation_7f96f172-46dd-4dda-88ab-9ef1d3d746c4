package com.mira.core.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;

import java.security.KeyPair;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

import static org.junit.jupiter.api.Assertions.*;

/**
 * RsaUtil 测试类
 * 
 * <AUTHOR>
 */
public class RsaUtilTest {
    
    private String publicKey;
    private String privateKey;
    
    @BeforeEach
    public void setUp() throws NoSuchAlgorithmException {
        // 生成测试用的密钥对
        KeyPair keyPair = RsaUtil.generateKeyPair();
        publicKey = RsaUtil.keyToBase64String(keyPair.getPublic());
        privateKey = RsaUtil.keyToBase64String(keyPair.getPrivate());
        
        System.out.println("Test Public Key: " + publicKey);
        System.out.println("Test Private Key: " + privateKey);
    }
    
    @Test
    @DisplayName("测试基本的加密解密功能")
    public void testBasicEncryptDecrypt() {
        String originalData = "Hello, RSA World! 测试中文字符";
        
        // 加密
        String encrypted = RsaUtil.encryptionRsa(originalData, publicKey);
        assertNotNull(encrypted);
        assertNotEquals(originalData, encrypted);
        System.out.println("Encrypted: " + encrypted);
        
        // 解密
        String decrypted = RsaUtil.decodeRsa(encrypted, privateKey);
        assertEquals(originalData, decrypted);
        System.out.println("Decrypted: " + decrypted);
    }
    
    @Test
    @DisplayName("测试JSON数据的加密解密")
    public void testJsonEncryptDecrypt() {
        Map<String, Object> data = new HashMap<>();
        data.put("userId", 12345L);
        data.put("email", "<EMAIL>");
        data.put("registerTimestamp", System.currentTimeMillis());
        
        String json = JsonUtil.toJson(data);
        System.out.println("Original JSON: " + json);
        
        // 加密
        String encrypted = RsaUtil.encryptionRsa(json, publicKey);
        assertNotNull(encrypted);
        System.out.println("Encrypted JSON: " + encrypted);
        
        // 解密
        String decrypted = RsaUtil.decodeRsa(encrypted, privateKey);
        assertEquals(json, decrypted);
        System.out.println("Decrypted JSON: " + decrypted);
        
        // 验证解密后的JSON可以正确解析
        Map<String, Object> decryptedMap = JsonUtil.toObject(decrypted, HashMap.class);
        assertEquals(data.get("userId").toString(), decryptedMap.get("userId").toString());
        assertEquals(data.get("email"), decryptedMap.get("email"));
    }
    
    @Test
    @DisplayName("测试URL编码的Base64数据解密")
    public void testUrlEncodedBase64Decrypt() {
        String originalData = "Test URL encoded Base64";
        
        // 加密
        String encrypted = RsaUtil.encryptionRsa(originalData, publicKey);
        
        // 模拟URL编码的影响
        String urlSafeBase64 = encrypted.replace('+', '-').replace('/', '_').replace("=", "");
        System.out.println("URL Safe Base64: " + urlSafeBase64);
        
        // 应该能够正确解密URL安全的Base64
        String decrypted = RsaUtil.decodeRsa(urlSafeBase64, privateKey);
        assertEquals(originalData, decrypted);
    }
    
    @Test
    @DisplayName("测试带换行的Base64数据解密")
    public void testBase64WithNewlines() {
        String originalData = "Test Base64 with newlines";
        
        // 加密
        String encrypted = RsaUtil.encryptionRsa(originalData, publicKey);
        
        // 添加换行符（模拟MIME格式）
        StringBuilder mimeBase64 = new StringBuilder();
        for (int i = 0; i < encrypted.length(); i += 76) {
            mimeBase64.append(encrypted, i, Math.min(i + 76, encrypted.length()));
            if (i + 76 < encrypted.length()) {
                mimeBase64.append("\n");
            }
        }
        System.out.println("MIME Base64:\n" + mimeBase64);
        
        // 应该能够正确解密带换行的Base64
        String decrypted = RsaUtil.decodeRsa(mimeBase64.toString(), privateKey);
        assertEquals(originalData, decrypted);
    }
    
    @Test
    @DisplayName("测试签名和验签功能")
    public void testSignAndVerify() {
        TreeMap<String, Object> data = new TreeMap<>();
        data.put("userId", 12345L);
        data.put("email", "<EMAIL>");
        data.put("timestamp", System.currentTimeMillis());
        
        // 生成签名
        String signature = RsaUtil.generateSign(data, privateKey);
        assertNotNull(signature);
        System.out.println("Signature: " + signature);
        
        // 验证签名
        Boolean verified = RsaUtil.verifySign(data, signature, publicKey);
        assertTrue(verified);
        
        // 修改数据后验证应该失败
        data.put("userId", 54321L);
        Boolean verifiedAfterModification = RsaUtil.verifySign(data, signature, publicKey);
        assertFalse(verifiedAfterModification);
    }
    
    @Test
    @DisplayName("测试空数据处理")
    public void testNullAndEmptyData() {
        // 测试空数据加密
        assertThrows(IllegalArgumentException.class, () -> {
            RsaUtil.decodeRsa(null, privateKey);
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            RsaUtil.decodeRsa("", privateKey);
        });
        
        // 测试空密钥
        assertThrows(IllegalArgumentException.class, () -> {
            RsaUtil.decodeRsa("somedata", null);
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            RsaUtil.decodeRsa("somedata", "");
        });
    }
    
    @Test
    @DisplayName("测试错误的Base64数据")
    public void testInvalidBase64() {
        String invalidBase64 = "This is not a valid Base64 string!@#$%";
        
        assertThrows(RuntimeException.class, () -> {
            RsaUtil.decodeRsa(invalidBase64, privateKey);
        });
    }
    
    @Test
    @DisplayName("测试PEM格式密钥")
    public void testPemFormatKeys() {
        // 创建PEM格式的密钥
        String pemPublicKey = "-----BEGIN PUBLIC KEY-----\n" + 
                              publicKey + "\n" +
                              "-----END PUBLIC KEY-----";
        String pemPrivateKey = "-----BEGIN PRIVATE KEY-----\n" + 
                               privateKey + "\n" +
                               "-----END PRIVATE KEY-----";
        
        String originalData = "Test PEM format keys";
        
        // 使用PEM格式密钥加密
        String encrypted = RsaUtil.encryptionRsa(originalData, pemPublicKey);
        assertNotNull(encrypted);
        
        // 使用PEM格式密钥解密
        String decrypted = RsaUtil.decodeRsa(encrypted, pemPrivateKey);
        assertEquals(originalData, decrypted);
    }
    
    @Test
    @DisplayName("模拟实际场景：注册确认流程")
    public void testRegisterConfirmScenario() {
        // 模拟注册时生成的数据
        Map<String, Object> registerData = new HashMap<>();
        registerData.put("userId", 10001L);
        registerData.put("registerTimestamp", System.currentTimeMillis());
        registerData.put("email", "<EMAIL>");
        
        String json = JsonUtil.toJson(registerData);
        System.out.println("Register data: " + json);
        
        // 加密
        String encrypted = RsaUtil.encryptionRsa(json, publicKey);
        System.out.println("Encrypted hash: " + encrypted);
        
        // 模拟URL传输（URL编码）
        String urlEncoded = encrypted.replace("+", "%2B")
                                     .replace("/", "%2F")
                                     .replace("=", "%3D");
        System.out.println("URL encoded: " + urlEncoded);
        
        // 模拟接收端处理
        // 1. URL解码
        String urlDecoded = urlEncoded.replace("%2B", "+")
                                      .replace("%2F", "/")
                                      .replace("%3D", "=");
        
        // 2. RSA解密
        String decrypted = RsaUtil.decodeRsa(urlDecoded, privateKey);
        System.out.println("Decrypted: " + decrypted);
        
        // 3. 解析JSON
        Map<String, Object> decryptedData = JsonUtil.toObject(decrypted, HashMap.class);
        assertEquals(registerData.get("userId").toString(), decryptedData.get("userId").toString());
        assertEquals(registerData.get("email"), decryptedData.get("email"));
    }
}