package com.mira.sso.handler;

import com.mira.api.thirdparty.dto.blog.Ip2CountryDTO;
import com.mira.api.thirdparty.dto.shopify.CustomerInfoDTO;
import com.mira.api.thirdparty.dto.shopify.CustomerRegisterDTO;
import com.mira.api.thirdparty.provider.IBlogProvider;
import com.mira.api.thirdparty.provider.IShopifyProvider;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * third party handler
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ThirdParthHandler {
    @Resource
    private IBlogProvider blogProvider;
    @Resource
    private IShopifyProvider shopifyProvider;

    @CircuitBreaker(name = "mira-third-party", fallbackMethod = "ip2CountryFallback")
    public Ip2CountryDTO ip2Country(String ip, Long userId) {
        return blogProvider.getCountryByIp(ip, userId).getData();
    }

    public Ip2CountryDTO ip2CountryFallback(String ip, Long userId, Exception ex) {
        log.error("ip2CountryFallback: {}", ex.getMessage());
        return null;
    }

    @CircuitBreaker(name = "mira-third-party", fallbackMethod = "shopifyCheckAccountExistFallback")
    public boolean shopifyCheckAccountExist(String email, String currency) {
        return shopifyProvider.checkAccountExist(email, currency).getData();
    }

    public boolean shopifyCheckAccountExistFallback(String email, String currency, Exception ex) {
        log.error("shopifyCheckAccountExistFallback: {}", ex.getMessage());
        return false;
    }

    @CircuitBreaker(name = "mira-third-party", fallbackMethod = "shopifyCreateAccountFallback")
    public void shopifyCreateAccount(CustomerRegisterDTO customerRegisterDTO) {
        shopifyProvider.createAccount(customerRegisterDTO);
    }

    public void shopifyCreateAccountFallback(CustomerRegisterDTO customerRegisterDTO, Exception ex) {
        log.error("shopifyCreateAccountFallback: {}", ex.getMessage());
    }

    @CircuitBreaker(name = "mira-third-party", fallbackMethod = "shopifyUpdateCustomerPasswordFallback")
    public void shopifyUpdateCustomerPassword(CustomerInfoDTO customerInfoDTO) {
        shopifyProvider.updateCustomerPassword(customerInfoDTO);
    }

    public void shopifyUpdateCustomerPasswordFallback(CustomerInfoDTO customerInfoDTO, Exception ex) {
        log.error("shopifyUpdateCustomerPasswordFallback: {}", ex.getMessage());
    }

    @CircuitBreaker(name = "mira-third-party", fallbackMethod = "shopifyUpdateCustomerEmailFallback")
    public void shopifyUpdateCustomerEmail(CustomerInfoDTO customerInfoDTO) {
        shopifyProvider.updateCustomerEmail(customerInfoDTO);
    }

    public void shopifyUpdateCustomerEmailFallback(CustomerInfoDTO customerInfoDTO, Exception ex) {
        log.error("shopifyUpdateCustomerEmailFallback: {}", ex.getMessage());
    }

    @CircuitBreaker(name = "mira-third-party", fallbackMethod = "shopifyDefaultAddressFallback")
    public Map<String, Object> shopifyDefaultAddress(String email, String currency) {
        return shopifyProvider.defaultAddress(email, currency).getData();
    }

    public Map<String, Object> shopifyDefaultAddressFallback(String email, String currency, Exception ex) {
        log.error("shopifyDefaultAddressFallback: {}", ex.getMessage());
        return null;
    }
}
