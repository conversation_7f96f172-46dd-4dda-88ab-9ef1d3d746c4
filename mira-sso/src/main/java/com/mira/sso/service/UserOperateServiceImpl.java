package com.mira.sso.service;

import com.mira.api.clinic.provider.IClinicProvider;
import com.mira.api.thirdparty.dto.shopify.CustomerInfoDTO;
import com.mira.api.user.consts.RedisCacheKeyConst;
import com.mira.core.consts.HeaderConst;
import com.mira.core.consts.RequestSendFlag;
import com.mira.core.consts.enums.BizCodeEnum;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.response.enums.BaseCodeEnum;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.PasswordUtil;
import com.mira.core.util.RsaUtil;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import com.mira.sso.consts.UserConst;
import com.mira.sso.controller.vo.UserForgetPasswordVO;
import com.mira.sso.dal.dao.*;
import com.mira.sso.dal.entity.*;
import com.mira.sso.exception.SsoException;
import com.mira.sso.handler.ThirdParthHandler;
import com.mira.sso.producer.EmailProducer;
import com.mira.sso.service.dto.ChangeEmailDTO;
import com.mira.sso.service.dto.ResetPasswordDTO;
import com.mira.sso.service.manager.CacheManager;
import com.mira.web.properties.RsaProperties;
import com.mira.web.util.HashDecodeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 用户信息操作接口实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class UserOperateServiceImpl implements IUserOperateService {
    @Resource
    private AppUserDAO appUserDAO;
    @Resource
    private AppUserInfoDAO appUserInfoDAO;
    @Resource
    private AppUserPartnerDAO appUserPartnerDAO;
    @Resource
    private UserGoalTrialDAO userGoalTrialDAO;
    @Resource
    private UserProductTrialDAO userProductTrialDAO;
    @Resource
    private RsaProperties rsaProperties;
    @Resource
    private CacheManager cacheManager;
    @Resource
    private EmailProducer emailProducer;
    @Resource
    private ThirdParthHandler thirdParthHandler;
    @Resource
    private IClinicProvider clinicProvider;

    @Override
    public UserForgetPasswordVO resetPassword(String email, int flag) {
        AppUserEntity appUser = appUserDAO.getByEmail(email);
        if (Objects.isNull(appUser)) {
            throw new SsoException("The account doesn't exist.");
        }

        // 发送邮件
        AppUserInfoEntity appUserInfo = appUserInfoDAO.getByUserId(appUser.getId());
        Map<String, String> emailVariable = emailProducer.userResetPassword(appUser, appUserInfo);

        // save cache
        String hash = emailVariable.get("userHash");
        cacheManager.cacheResetPassword(appUser, hash, flag);

        // result
        UserForgetPasswordVO userForgetPasswordVO = new UserForgetPasswordVO();
        userForgetPasswordVO.setEmailFlag(true);

        log.info("user:{} reset password, flag:{}", appUser.getId(), flag);
        return userForgetPasswordVO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String resetPasswordConfirm(ResetPasswordDTO resetPasswordDTO) {
        String hash = HashDecodeUtil.decodeHash(resetPasswordDTO.getHash());
        String decode = RsaUtil.decodeRsa(hash, rsaProperties.getPrivateKey());
        Map<String, Object> infoMap = JsonUtil.toObject(decode, HashMap.class);
        Long userId = Long.valueOf(infoMap.get("userId").toString());

        // check
        Map<String, Object> cacheResetPasswordMap = cacheManager.getCacheResetPassword(userId);
        if (MapUtils.isEmpty(cacheResetPasswordMap)) {
            throw new SsoException(BizCodeEnum.LINK_HAS_EXPIRED.getCode(),
                    "Time's up on this link. Request a new one to reset your password.");
        }
        String currentHash = cacheResetPasswordMap.get("hash").toString();
        if (StringUtils.isNotBlank(currentHash) && !currentHash.equals(hash)) {
            throw new SsoException(BizCodeEnum.LINK_HAS_EXPIRED.getCode(),
                    "Time's up on this link. Request a new one to reset your password.");
        }
        if (!PasswordUtil.checkRules(resetPasswordDTO.getPw())) {
            throw new SsoException("8-30 characters with numbers and letters. Make it fun!");
        }

        // 更新密码
        AppUserEntity appUser = appUserDAO.getById(userId);
        appUser.setSalt(PasswordUtil.generateSalt(20));
        appUser.setPassword(PasswordUtil.encryptPassword(resetPasswordDTO.getPw(), appUser.getSalt()));
        appUser.setPasswordGrade(UserConst.PASSWORD_GRADE_HIGHEST);
        UpdateEntityTimeUtil.updateBaseEntityTime(ContextHolder.get(HeaderConst.TIME_ZONE), appUser);
        appUserDAO.updateById(appUser);
        cacheManager.deleteUserDetailCache(userId);

        // 移除登录锁定标记和错误次数
        cacheManager.cacheDelete(userId, RedisCacheKeyConst.USER_LOGIN_LOCK);
        cacheManager.cacheDelete(appUser.getId(), RedisCacheKeyConst.USER_LOGIN_ERROR);

        // 如果shopify存在，需要重置shopify的密码
        try {
            String currency = ContextHolder.get(HeaderConst.CURRENCY);
            if (thirdParthHandler.shopifyCheckAccountExist(appUser.getEmail(), currency)) {
                thirdParthHandler.shopifyUpdateCustomerPassword(new CustomerInfoDTO()
                        .setEmail(appUser.getEmail())
                        .setPassword(appUser.getPassword())
                        .setCurrency(ContextHolder.get(HeaderConst.CURRENCY)));
            }
        } catch (Exception e) {
            log.error("shopify reset password error, email:{}", appUser.getEmail(), e);
        }

        log.info("user:{} confirm reset password, password:{}", userId, resetPasswordDTO.getPw());
        return BaseCodeEnum.OK.getMsg();
    }

    @Override
    public String resetPasswordStatus(String email) {
        AppUserEntity appUser = appUserDAO.getByEmail(email);
        if (Objects.isNull(appUser)) {
            throw new SsoException("The account doesn't exist.");
        }
        Map<String, Object> cacheResetPasswordInfo = cacheManager.getCacheResetPassword(appUser.getId());
        if (MapUtils.isEmpty(cacheResetPasswordInfo)) {
            throw new SsoException(BizCodeEnum.LINK_HAS_EXPIRED.getCode(), "Time's up on this link. Request a new one.");
        }
        if (appUser.getPassword().equals(cacheResetPasswordInfo.get("oldPwEncrypt"))) {
            throw new SsoException(BizCodeEnum.EMAIL_USER_WILL_CHANGE);
        }

        return "Reset password success.";
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String changeEmail(ChangeEmailDTO changeEmailDTO, int flag) {
        String newEmail = changeEmailDTO.getNewEmail();
        String oldEmail = changeEmailDTO.getOldEmail();

        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        if (!loginInfo.getUsername().equals(oldEmail)) {
            throw new SsoException("Your old email isn't match.");
        }
        if (RequestSendFlag.FIRST_SEND == flag) {
            AppUserEntity newEmailUser = appUserDAO.getByEmail(newEmail);
            if (Objects.nonNull(newEmailUser)) {
                throw new SsoException("Looks like that email is already in use. Try another?");
            }
            AppUserPartnerEntity appUserPartner = appUserPartnerDAO.getByEmail(newEmail);
            if (Objects.nonNull(appUserPartner)) {
                throw new SsoException("Looks like that email is already in use. Try another?");
            }
        }

        // 发送邮件
        AppUserInfoEntity appUserInfo = appUserInfoDAO.getByUserId(loginInfo.getId());
        Map<String, String> emailVariable = emailProducer.userChangeEmailSendNew(newEmail, appUserInfo);
        emailProducer.userChangeEmailSendOld(oldEmail, newEmail, appUserInfo);

        // save cache
        String hash = emailVariable.get("userHash");
        cacheManager.cacheChangeEmail(loginInfo.getId(), changeEmailDTO, hash, flag);

        log.info("user:{} will edit email, oldEmail:{}, newEmail:{}, flag:{}", loginInfo.getId(), oldEmail, newEmail, flag);
        return BaseCodeEnum.OK.getMsg();
    }

    @Override
    public String changeEmailConfirm(String hash) {
        hash = HashDecodeUtil.decodeHash(hash);
        String decode = RsaUtil.decodeRsa(hash, rsaProperties.getPrivateKey());
        Map<String, Object> infoMap = JsonUtil.toObject(decode, HashMap.class);
        Long userId = Long.valueOf(infoMap.get("userId").toString());
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);

        // check
        Map<String, Object> cacheChangeEmailInfo = cacheManager.getCacheChangeEmail(userId);
        if (MapUtils.isEmpty(cacheChangeEmailInfo)) {
            throw new SsoException(BizCodeEnum.LINK_HAS_EXPIRED.getCode(),
                    "Time's up on this link. Request a new one to change your email");
        }
        String currentHash = cacheChangeEmailInfo.get("hash").toString();
        if (StringUtils.isNotBlank(currentHash) && !currentHash.equals(hash)) {
            throw new SsoException(BizCodeEnum.LINK_HAS_EXPIRED.getCode(),
                    "Time's up on this link. Request a new one to change your email");
        }
        String newEmail = (String) cacheChangeEmailInfo.get("newEmail");

        changeUserEmail(userId, timeZone, newEmail, "self");
        return BaseCodeEnum.OK.getMsg();
    }

    /**
     * 修改用户邮箱
     *
     * @param source   self || backend
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void changeUserEmail(Long userId, String timeZone, String newEmail, String source) {
        // app user
        AppUserEntity appUser = appUserDAO.getById(userId);
        appUser.setEmail(newEmail);
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, appUser);
        appUserDAO.updateById(appUser);
        cacheManager.deleteUserDetailCache(userId);

        // partner
        AppUserPartnerEntity partner = appUserPartnerDAO.getByUserId(appUser.getId());
        if (Objects.nonNull(partner)) {
            partner.setUserEmail(appUser.getEmail());
            UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, partner);
            appUserPartnerDAO.updateById(partner);
        }

        try {
            // patient
            clinicProvider.updatePatientEmail(userId, appUser.getEmail());
            // beta user
            UserGoalTrialEntity goalTrial = userGoalTrialDAO.getByUserId(userId);
            UserProductTrialEntity productTrial = userProductTrialDAO.getByUserId(userId);
            if (Objects.nonNull(goalTrial)) {
                goalTrial.setEmail(appUser.getEmail());
                userGoalTrialDAO.updateById(goalTrial);
            }
            if (Objects.nonNull(productTrial)) {
                productTrial.setEmail(appUser.getEmail());
                userProductTrialDAO.updateById(productTrial);
            }
        } catch (Exception e) {
            log.error("update patient or beta error, userId:{}", userId);
        }

        // shopify
        thirdParthHandler.shopifyUpdateCustomerEmail(new CustomerInfoDTO()
                .setEmail(appUser.getEmail())
                .setCurrency(ContextHolder.get(HeaderConst.CURRENCY)));

        log.info("user:{} change email {} from source:{}", userId, appUser.getEmail(), source);
    }

    @Override
    public String changeEmailStatus(ChangeEmailDTO changeEmailDTO) {
        String newEmail = changeEmailDTO.getNewEmail();
        String oldEmail = changeEmailDTO.getOldEmail();
        AppUserEntity appUser = appUserDAO.getByEmail(oldEmail);
        if (Objects.isNull(appUser)) {
            AppUserEntity newAppUser = appUserDAO.getByEmail(newEmail);
            if (Objects.isNull(newAppUser)) {
                throw new SsoException("Oops! Looks like this email is invalid. Please check if it’s spelled right");
            }
            return "Change email success.";
        } else {
            if (!cacheManager.cacheExist(appUser.getId(), RedisCacheKeyConst.EMAIL_CHANGE_EMAIL)) {
                throw new SsoException(BizCodeEnum.LINK_HAS_EXPIRED.getCode(),
                        "Time's up on this link. Request a new one to change your email");
            }
            throw new SsoException(BizCodeEnum.EMAIL_USER_WILL_CHANGE.getCode(),
                    "We sent you an email. Please confirm to continue.");
        }
    }
}
